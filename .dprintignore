# Ignore common directories that consume memory
node_modules/
dist/
build/
.next/
.turbo/
coverage/
playwright-report/
test-results/
.vercel/
.git/
.trunk/
.claude/
.github/
.vscode/
.idea/
.trae/
.ruler/
.serena/
archon/
serena/
temp-*/
tmp.*/
.tmp/
archives/
backups/
deprecated/
legacy/
supabase/migrations/
supabase/types/
*.generated.*
*.d.ts

# Ignore large files that might consume memory
*.log
*.cache
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig
*.swp
*.swo
*~

# Ignore binary and media files
*.jpg
*.jpeg
*.png
*.gif
*.ico
*.svg
*.pdf
*.zip
*.tar
*.gz
*.rar
*.7z
*.exe
*.dll
*.so
*.dylib
*.woff
*.woff2
*.ttf
*.eot

# Ignore lock files and package manager files
package-lock.json
yarn.lock
bun.lockb
pnpm-lock.yaml

# Ignore generated documentation
**/docs/api/**
**/docs/generated/**
**/storybook-static/**