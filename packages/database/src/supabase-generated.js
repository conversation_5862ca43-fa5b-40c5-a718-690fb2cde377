export const Constants = {
    public: {
        Enums: {
            api_provider_type: [
                "cfm",
                "crm_state",
                "coren",
                "cro",
                "crf",
                "crefito",
                "crn",
                "crp",
                "anvisa",
                "custom_api",
            ],
            api_verification_status: [
                "pending",
                "in_progress",
                "success",
                "failed",
                "error",
                "timeout",
                "rate_limited",
                "unauthorized",
                "not_found",
            ],
            audit_type: [
                "credential_verification",
                "compliance_check",
                "document_review",
                "external_verification",
                "periodic_audit",
                "incident_investigation",
                "regulatory_inspection",
            ],
            automation_status: [
                "active",
                "inactive",
                "paused",
                "error",
                "maintenance",
            ],
            automation_trigger_type: [
                "schedule",
                "expiry_check",
                "compliance_check",
                "api_sync",
                "event_triggered",
                "manual",
            ],
            churn_risk_level: ["low", "medium", "high", "critical"],
            compliance_status: [
                "compliant",
                "non_compliant",
                "pending_review",
                "under_investigation",
                "remediation_required",
                "exempted",
            ],
            engagement_event_type: [
                "appointment_scheduled",
                "appointment_completed",
                "appointment_cancelled",
                "email_opened",
                "email_clicked",
                "sms_received",
                "app_login",
                "profile_updated",
                "payment_completed",
                "review_submitted",
                "referral_made",
            ],
            execution_status: [
                "pending",
                "running",
                "completed",
                "failed",
                "cancelled",
            ],
            integration_method: [
                "api_rest",
                "api_soap",
                "database_direct",
                "file_transfer",
                "hl7_fhir",
                "hl7_v2",
                "webhook",
                "message_queue",
                "custom_protocol",
            ],
            integration_status: [
                "active",
                "inactive",
                "error",
                "testing",
                "maintenance",
                "deprecated",
            ],
            legacy_system_type: [
                "ehr",
                "his",
                "pacs",
                "lis",
                "crm",
                "erp",
                "billing",
                "scheduling",
                "hr",
                "inventory",
                "custom",
            ],
            notification_channel: [
                "email",
                "sms",
                "whatsapp",
                "push_notification",
                "in_app",
                "webhook",
            ],
            permission_action: [
                "view",
                "create",
                "update",
                "delete",
                "verify",
                "approve",
                "audit",
                "export",
                "manage_access",
            ],
            professional_certification_type: [
                "specialty_certification",
                "continuing_education",
                "training_certificate",
                "competency_certification",
                "external_credential",
                "international_certification",
            ],
            professional_registration_type: [
                "medical_license",
                "nursing_license",
                "dentist_license",
                "pharmacist_license",
                "physiotherapy_license",
                "nutrition_license",
                "psychology_license",
                "biomedicine_license",
                "other",
            ],
            resource_type: [
                "professional_registrations",
                "professional_certifications",
                "compliance_assessments",
                "audit_logs",
                "api_configurations",
                "compliance_alerts",
                "reports",
                "system_settings",
            ],
            retention_strategy_type: [
                "email_campaign",
                "sms_reminder",
                "discount_offer",
                "personalized_content",
                "loyalty_program",
                "referral_incentive",
                "appointment_reminder",
                "birthday_campaign",
                "reactivation_campaign",
                "feedback_request",
            ],
            sync_direction: ["bidirectional", "inbound", "outbound"],
        },
    },
};
