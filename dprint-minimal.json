{"$schema": "https://dprint.dev/schemas/v0.json", "includes": ["apps/web/src/**/*.{ts,tsx}", "packages/ui/src/**/*.{ts,tsx}"], "excludes": ["**/node_modules", "**/dist", "**/build", "**/.next", "**/.turbo", "**/coverage", "**/.git"], "plugins": ["https://plugins.dprint.dev/typescript-0.91.1.wasm"], "typescript": {"lineWidth": 100, "indentWidth": 2, "useTabs": false, "semiColons": "always", "quoteStyle": "alwaysDouble", "trailingCommas": "onlyMultiLine"}}